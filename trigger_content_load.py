#!/usr/bin/env python3
"""
Try to trigger actual content loading on metruyencv.com
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def trigger_content_load():
    """Try different methods to trigger content loading"""
    
    url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    print("🔄 Triggering Content Load")
    print("=" * 50)
    
    async with MetruyenScraper() as scraper:
        await scraper.start()
        page = scraper.scraper_engine.page
        
        print("🚀 Loading page...")
        await page.goto(url, wait_until='networkidle')
        await asyncio.sleep(5)
        
        # Method 1: Try to simulate user interaction
        print("👆 Method 1: Simulating user interactions...")
        
        # Scroll to content area
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
        await asyncio.sleep(2)
        
        # Try clicking on the chapter content area
        try:
            content_element = await page.query_selector('#chapter-content')
            if content_element:
                await content_element.click()
                print("   ✅ Clicked on #chapter-content")
                await asyncio.sleep(3)
        except Exception as e:
            print(f"   ❌ Click failed: {e}")
        
        # Check content after interaction
        content_check1 = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                return element ? {
                    innerHTML: element.innerHTML,
                    textContent: element.textContent,
                    length: element.textContent ? element.textContent.length : 0
                } : null;
            }
        """)
        
        print(f"   Content after interaction: {content_check1['length'] if content_check1 else 0} chars")
        
        # Method 2: Try to trigger focus/visibility events
        print("👁️ Method 2: Triggering visibility events...")
        
        await page.evaluate("""
            () => {
                // Trigger various events that might load content
                const events = ['focus', 'click', 'mouseenter', 'scroll', 'resize', 'load'];
                const element = document.getElementById('chapter-content');
                
                if (element) {
                    events.forEach(eventType => {
                        const event = new Event(eventType, { bubbles: true });
                        element.dispatchEvent(event);
                    });
                }
                
                // Also trigger on document and window
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    document.dispatchEvent(event);
                    if (eventType !== 'resize') {
                        window.dispatchEvent(event);
                    }
                });
                
                // Trigger resize specifically
                window.dispatchEvent(new Event('resize'));
            }
        """)
        
        await asyncio.sleep(3)
        
        # Check content after events
        content_check2 = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                return element ? {
                    innerHTML: element.innerHTML,
                    textContent: element.textContent,
                    length: element.textContent ? element.textContent.length : 0
                } : null;
            }
        """)
        
        print(f"   Content after events: {content_check2['length'] if content_check2 else 0} chars")
        
        # Method 3: Try to wait for network requests
        print("🌐 Method 3: Monitoring network requests...")
        
        # Set up network monitoring
        network_requests = []
        
        def handle_request(request):
            network_requests.append({
                'url': request.url,
                'method': request.method,
                'headers': dict(request.headers)
            })
        
        page.on('request', handle_request)
        
        # Trigger some actions that might cause network requests
        await page.evaluate("""
            () => {
                // Try to trigger any AJAX requests
                if (window.jQuery) {
                    console.log('jQuery available');
                }
                
                if (window.fetch) {
                    console.log('Fetch available');
                }
                
                // Look for any global functions that might load content
                const globalFunctions = Object.getOwnPropertyNames(window).filter(name => 
                    typeof window[name] === 'function' && (
                        name.toLowerCase().includes('load') ||
                        name.toLowerCase().includes('fetch') ||
                        name.toLowerCase().includes('get') ||
                        name.toLowerCase().includes('content')
                    )
                );
                
                console.log('Found potential loading functions:', globalFunctions);
                
                // Try to call some of them
                for (const funcName of globalFunctions.slice(0, 5)) {
                    try {
                        if (window[funcName].length === 0) {
                            window[funcName]();
                            console.log('Called:', funcName);
                        }
                    } catch (e) {
                        console.log('Failed to call:', funcName, e.message);
                    }
                }
            }
        """)
        
        await asyncio.sleep(5)
        
        print(f"   Network requests captured: {len(network_requests)}")
        for req in network_requests[-5:]:  # Show last 5 requests
            print(f"     {req['method']} {req['url']}")
        
        # Method 4: Try to manually decrypt the content
        print("🔓 Method 4: Manual decryption attempts...")
        
        decryption_result = await page.evaluate("""
            () => {
                const chapterData = window.chapterData;
                if (!chapterData || !chapterData.content) {
                    return { success: false, reason: 'No chapterData' };
                }
                
                const encryptedContent = chapterData.content;
                console.log('Encrypted content:', encryptedContent);
                
                // Try different decryption methods
                const methods = [
                    // Method 1: Simple Base64 decode
                    () => {
                        try {
                            return atob(encryptedContent);
                        } catch (e) {
                            return null;
                        }
                    },
                    
                    // Method 2: URL decode then Base64
                    () => {
                        try {
                            return atob(decodeURIComponent(encryptedContent));
                        } catch (e) {
                            return null;
                        }
                    },
                    
                    // Method 3: Try to find if there's a hidden decryption key
                    () => {
                        try {
                            // Look for any hidden elements or data attributes that might contain keys
                            const hiddenElements = document.querySelectorAll('[data-key], [data-decrypt], .hidden');
                            for (const el of hiddenElements) {
                                const key = el.dataset.key || el.dataset.decrypt || el.textContent;
                                if (key && key.length > 5) {
                                    console.log('Found potential key:', key);
                                    // Try simple XOR
                                    let result = '';
                                    for (let i = 0; i < encryptedContent.length; i++) {
                                        result += String.fromCharCode(encryptedContent.charCodeAt(i) ^ key.charCodeAt(i % key.length));
                                    }
                                    if (result.includes('Chương') || result.includes('chương')) {
                                        return result;
                                    }
                                }
                            }
                            return null;
                        } catch (e) {
                            return null;
                        }
                    }
                ];
                
                for (let i = 0; i < methods.length; i++) {
                    try {
                        const result = methods[i]();
                        if (result && typeof result === 'string' && result.length > 50) {
                            console.log(`Method ${i+1} result:`, result.substring(0, 100));
                            
                            // Check if this looks like Vietnamese content
                            if (result.includes('Chương') || result.includes('chương') || 
                                result.includes('Nhìn') || result.includes('nhìn') ||
                                result.includes('<br>') || result.includes('<div>')) {
                                
                                // Try to inject it
                                const element = document.getElementById('chapter-content');
                                if (element) {
                                    element.innerHTML = result;
                                    return { 
                                        success: true, 
                                        method: i+1, 
                                        content: result,
                                        length: result.length 
                                    };
                                }
                            }
                        }
                    } catch (e) {
                        console.log(`Method ${i+1} failed:`, e.message);
                    }
                }
                
                return { success: false, reason: 'All decryption methods failed' };
            }
        """)
        
        print(f"   Decryption result: {decryption_result}")
        
        if decryption_result.get('success'):
            print(f"   ✅ Decryption successful with method {decryption_result['method']}!")
            print(f"   Content length: {decryption_result['length']}")
            print(f"   Content preview: {repr(decryption_result['content'][:200])}")
        
        # Final check
        print("🔍 Final content check...")
        
        final_content = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                return element ? {
                    innerHTML: element.innerHTML,
                    textContent: element.textContent,
                    innerText: element.innerText,
                    length: element.textContent ? element.textContent.length : 0,
                    visible: element.offsetParent !== null
                } : null;
            }
        """)
        
        if final_content:
            print(f"   Final content length: {final_content['length']}")
            print(f"   Visible: {final_content['visible']}")
            
            if final_content['length'] > 100:
                print(f"   ✅ Content successfully loaded!")
                print(f"   Preview: {repr(final_content['textContent'][:300])}")
                return True
            else:
                print(f"   ❌ No substantial content found")
        
        # Take screenshot for debugging
        await page.screenshot(path=f"trigger_content_{int(asyncio.get_event_loop().time())}.png")
        print(f"   📸 Screenshot saved for debugging")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(trigger_content_load())
    print(f"\n🎯 Result: {'✅ Success' if success else '❌ Failed'}")
