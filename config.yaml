logging:
  file: scraper.log
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}'
  level: INFO
output:
  csv:
    delimiter: ','
    encoding: utf-8-sig
  formats:
  - json
  - csv
  json:
    encoding: utf-8
    pretty: true
scraper:
  authentication:
    api_endpoint: https://backend.metruyencv.com/api/auth/login
    auto_refresh: true
    credentials:
      email: <EMAIL>
      password: Anhvip@522
      remember: 1
    enabled: true
    token_storage: auth_token.json
  browser:
    headless: true
    timeout: 30000
    viewport:
      height: 1080
      width: 1920
  retry:
    backoff_factor: 2
    max_attempts: 3
    timeout: 60
  stealth:
    delays:
      max_delay: 5
      min_delay: 2
      page_load_delay: 3
    proxy:
      enabled: false
      list: []
      rotation: false
    user_agents:
    - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko)
      Chrome/120.0.0.0 Safari/537.36
    - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like
      Gecko) Chrome/120.0.0.0 Safari/537.36
    - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0
      Safari/537.36
targets:
  metruyencv:
    base_url: https://metruyencv.com
    patterns:
      chapter_url: /truyen/.+/chuong-\d+
      story_url: /truyen/.+
    selectors:
      chapter_nav: a[href*='chuong']
      content: '#chapter-content'
      locked_content: .chapter-locked, [data-locked], .vip-content, .premium-content
      next_chapter: button[data-x-ref='nextId']
      prev_chapter: button[data-x-ref='prevId']
      title: h1, .title, [class*='title']
    wait_conditions:
    - networkidle
    - domcontentloaded
