"""
Error Handling and Retry Logic Module
Provides robust error handling, retry mechanisms, and recovery strategies
"""

import asyncio
import time
from typing import Any, Callable, Optional, Dict, List, Union
from functools import wraps
from enum import Enum
from loguru import logger


class ErrorType(Enum):
    """Types of errors that can occur during scraping"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    CAPTCHA_ERROR = "captcha_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    CONTENT_ERROR = "content_error"
    AUTHENTICATION_ERROR = "auth_error"
    UNKNOWN_ERROR = "unknown_error"


class ScrapingError(Exception):
    """Custom exception for scraping errors"""
    
    def __init__(self, message: str, error_type: ErrorType, url: str = None, retry_after: int = None):
        super().__init__(message)
        self.error_type = error_type
        self.url = url
        self.retry_after = retry_after
        self.timestamp = time.time()


class ErrorHandler:
    """Handles errors and implements retry logic"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.retry_config = self.config.get_retry_config()
        self.error_stats: Dict[str, int] = {}
        self.failed_urls: List[Dict[str, Any]] = []
    
    def retry_on_error(self, max_attempts: Optional[int] = None, 
                      backoff_factor: Optional[float] = None,
                      retry_on: Optional[List[ErrorType]] = None):
        """Decorator for automatic retry on errors"""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                attempts = max_attempts or self.retry_config.get('max_attempts', 3)
                backoff = backoff_factor or self.retry_config.get('backoff_factor', 2)
                retry_errors = retry_on or [
                    ErrorType.NETWORK_ERROR,
                    ErrorType.TIMEOUT_ERROR,
                    ErrorType.RATE_LIMIT_ERROR
                ]
                
                last_exception = None
                
                for attempt in range(attempts):
                    try:
                        return await func(*args, **kwargs)
                    
                    except ScrapingError as e:
                        last_exception = e
                        self._log_error(e, attempt + 1, attempts)
                        
                        if e.error_type not in retry_errors:
                            logger.error(f"Non-retryable error: {e}")
                            raise
                        
                        if attempt < attempts - 1:
                            delay = self._calculate_backoff_delay(attempt, backoff, e)
                            logger.info(f"Retrying in {delay} seconds...")
                            await asyncio.sleep(delay)
                        
                    except Exception as e:
                        last_exception = e
                        scraping_error = self._classify_error(e, kwargs.get('url'))
                        self._log_error(scraping_error, attempt + 1, attempts)
                        
                        if scraping_error.error_type not in retry_errors:
                            logger.error(f"Non-retryable error: {scraping_error}")
                            raise scraping_error
                        
                        if attempt < attempts - 1:
                            delay = self._calculate_backoff_delay(attempt, backoff, scraping_error)
                            logger.info(f"Retrying in {delay} seconds...")
                            await asyncio.sleep(delay)
                
                # All attempts failed
                logger.error(f"All {attempts} attempts failed")
                if isinstance(last_exception, ScrapingError):
                    raise last_exception
                else:
                    raise self._classify_error(last_exception, kwargs.get('url'))
            
            return wrapper
        return decorator
    
    def _classify_error(self, error: Exception, url: str = None) -> ScrapingError:
        """Classify generic exceptions into ScrapingError types"""
        error_message = str(error).lower()
        
        # Network-related errors
        if any(keyword in error_message for keyword in [
            'connection', 'network', 'dns', 'resolve', 'unreachable'
        ]):
            return ScrapingError(
                f"Network error: {error}",
                ErrorType.NETWORK_ERROR,
                url
            )
        
        # Timeout errors
        if any(keyword in error_message for keyword in [
            'timeout', 'timed out', 'time out'
        ]):
            return ScrapingError(
                f"Timeout error: {error}",
                ErrorType.TIMEOUT_ERROR,
                url
            )
        
        # Rate limiting
        if any(keyword in error_message for keyword in [
            'rate limit', 'too many requests', '429', 'throttle'
        ]):
            return ScrapingError(
                f"Rate limit error: {error}",
                ErrorType.RATE_LIMIT_ERROR,
                url,
                retry_after=60  # Default retry after 60 seconds
            )
        
        # CAPTCHA detection
        if any(keyword in error_message for keyword in [
            'captcha', 'recaptcha', 'hcaptcha', 'verify'
        ]):
            return ScrapingError(
                f"CAPTCHA error: {error}",
                ErrorType.CAPTCHA_ERROR,
                url
            )
        
        # Authentication errors
        if any(keyword in error_message for keyword in [
            'unauthorized', '401', '403', 'forbidden', 'access denied'
        ]):
            return ScrapingError(
                f"Authentication error: {error}",
                ErrorType.AUTHENTICATION_ERROR,
                url
            )
        
        # Default to unknown error
        return ScrapingError(
            f"Unknown error: {error}",
            ErrorType.UNKNOWN_ERROR,
            url
        )
    
    def _calculate_backoff_delay(self, attempt: int, backoff_factor: float, 
                               error: ScrapingError) -> float:
        """Calculate delay for exponential backoff"""
        base_delay = backoff_factor ** attempt
        
        # Add jitter to avoid thundering herd
        jitter = base_delay * 0.1 * (0.5 - hash(str(time.time())) % 100 / 100)
        
        # Respect retry_after for rate limiting
        if error.retry_after:
            base_delay = max(base_delay, error.retry_after)
        
        return base_delay + jitter
    
    def _log_error(self, error: ScrapingError, attempt: int, max_attempts: int) -> None:
        """Log error with context"""
        self._update_error_stats(error.error_type)
        
        log_message = f"Attempt {attempt}/{max_attempts} failed: {error}"
        if error.url:
            log_message += f" (URL: {error.url})"
        
        if attempt < max_attempts:
            logger.warning(log_message)
        else:
            logger.error(log_message)
            self._add_failed_url(error)
    
    def _update_error_stats(self, error_type: ErrorType) -> None:
        """Update error statistics"""
        error_key = error_type.value
        self.error_stats[error_key] = self.error_stats.get(error_key, 0) + 1
    
    def _add_failed_url(self, error: ScrapingError) -> None:
        """Add URL to failed list"""
        if error.url:
            self.failed_urls.append({
                'url': error.url,
                'error_type': error.error_type.value,
                'error_message': str(error),
                'timestamp': error.timestamp
            })
    
    async def handle_rate_limiting(self, delay: int = None) -> None:
        """Handle rate limiting with appropriate delay"""
        if delay is None:
            delay = self.retry_config.get('rate_limit_delay', 60)
        
        logger.warning(f"Rate limiting detected, waiting {delay} seconds...")
        await asyncio.sleep(delay)
    
    async def handle_captcha(self, page=None) -> bool:
        """Handle CAPTCHA challenges"""
        logger.warning("CAPTCHA detected - manual intervention may be required")
        
        if page:
            # Take screenshot for manual review
            try:
                screenshot_path = f"screenshots/errors/captcha_screenshot_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path)
                logger.info(f"CAPTCHA screenshot saved: {screenshot_path}")
            except Exception as e:
                logger.error(f"Failed to save CAPTCHA screenshot: {e}")
        
        # Wait for potential manual intervention
        await asyncio.sleep(30)
        return False  # Assume CAPTCHA not solved automatically
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = sum(self.error_stats.values())
        
        stats = {
            'total_errors': total_errors,
            'error_breakdown': self.error_stats.copy(),
            'failed_urls_count': len(self.failed_urls),
            'error_rate_by_type': {}
        }
        
        if total_errors > 0:
            for error_type, count in self.error_stats.items():
                stats['error_rate_by_type'][error_type] = count / total_errors
        
        return stats
    
    def get_failed_urls(self) -> List[Dict[str, Any]]:
        """Get list of failed URLs"""
        return self.failed_urls.copy()
    
    def clear_error_stats(self) -> None:
        """Clear error statistics"""
        self.error_stats.clear()
        self.failed_urls.clear()
        logger.info("Error statistics cleared")
    
    def should_continue_scraping(self, error_threshold: float = 0.5) -> bool:
        """Determine if scraping should continue based on error rate"""
        total_errors = sum(self.error_stats.values())
        
        if total_errors == 0:
            return True
        
        # Calculate critical error rate
        critical_errors = (
            self.error_stats.get(ErrorType.CAPTCHA_ERROR.value, 0) +
            self.error_stats.get(ErrorType.AUTHENTICATION_ERROR.value, 0)
        )
        
        critical_error_rate = critical_errors / total_errors
        
        if critical_error_rate > error_threshold:
            logger.warning(f"Critical error rate too high: {critical_error_rate:.2%}")
            return False
        
        return True
