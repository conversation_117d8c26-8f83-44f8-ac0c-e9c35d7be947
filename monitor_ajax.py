#!/usr/bin/env python3
"""
Monitor AJAX requests to see if content is loaded dynamically
"""

import asyncio
import sys
import json
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def monitor_ajax_requests():
    """Monitor all network requests to find content loading"""
    
    url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    print("🌐 Monitoring AJAX Requests")
    print("=" * 50)
    
    async with <PERSON><PERSON>yen<PERSON><PERSON>rap<PERSON>() as scraper:
        await scraper.start()
        page = scraper.scraper_engine.page
        
        # Store all requests and responses
        requests_log = []
        responses_log = []
        
        def handle_request(request):
            requests_log.append({
                'url': request.url,
                'method': request.method,
                'headers': dict(request.headers),
                'post_data': request.post_data
            })
            print(f"📤 REQUEST: {request.method} {request.url}")
        
        async def handle_response(response):
            try:
                # Only capture text responses that might contain content
                content_type = response.headers.get('content-type', '')
                if any(ct in content_type.lower() for ct in ['json', 'text', 'html']):
                    try:
                        response_text = await response.text()
                        responses_log.append({
                            'url': response.url,
                            'status': response.status,
                            'headers': dict(response.headers),
                            'content': response_text[:1000]  # First 1000 chars
                        })
                        print(f"📥 RESPONSE: {response.status} {response.url} ({len(response_text)} chars)")
                        
                        # Check if this response contains chapter content
                        if len(response_text) > 100 and any(keyword in response_text for keyword in ['Chương', 'chương', 'content', 'chapter']):
                            print(f"   🎯 Potential content response!")
                            print(f"   Preview: {repr(response_text[:200])}")
                    except Exception as e:
                        print(f"   ❌ Failed to read response: {e}")
            except Exception as e:
                print(f"   ❌ Response handling error: {e}")
        
        # Set up request/response monitoring
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        print("🚀 Loading page with monitoring...")
        await page.goto(url, wait_until='networkidle')
        
        print("⏳ Waiting for additional requests...")
        await asyncio.sleep(10)
        
        # Try to trigger content loading
        print("🔄 Triggering potential content loading...")
        
        # Scroll and interact
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await asyncio.sleep(2)
        
        # Try clicking various elements
        clickable_elements = await page.query_selector_all('button, a, .clickable, [onclick]')
        print(f"Found {len(clickable_elements)} clickable elements")
        
        for i, element in enumerate(clickable_elements[:5]):  # Try first 5
            try:
                text = await element.text_content()
                print(f"   Clicking element {i+1}: {repr(text[:50])}")
                await element.click()
                await asyncio.sleep(3)
            except Exception as e:
                print(f"   Click failed: {e}")
        
        # Wait for any additional requests
        await asyncio.sleep(5)
        
        print(f"\n📊 Summary:")
        print(f"Total requests: {len(requests_log)}")
        print(f"Total responses: {len(responses_log)}")
        
        # Analyze requests for potential content endpoints
        print(f"\n🔍 Analyzing requests...")
        
        for req in requests_log:
            url_lower = req['url'].lower()
            if any(keyword in url_lower for keyword in ['chapter', 'content', 'api', 'ajax', 'load']):
                print(f"   🎯 Potential content request:")
                print(f"     {req['method']} {req['url']}")
                if req['post_data']:
                    print(f"     POST data: {req['post_data']}")
        
        # Analyze responses for content
        print(f"\n🔍 Analyzing responses...")
        
        for resp in responses_log:
            if len(resp['content']) > 200:
                url_lower = resp['url'].lower()
                content_lower = resp['content'].lower()
                
                if any(keyword in content_lower for keyword in ['chương', 'chapter', 'content']):
                    print(f"   🎯 Response with potential content:")
                    print(f"     URL: {resp['url']}")
                    print(f"     Status: {resp['status']}")
                    print(f"     Content length: {len(resp['content'])}")
                    print(f"     Content preview: {repr(resp['content'][:300])}")
                    
                    # Try to parse as JSON
                    try:
                        json_data = json.loads(resp['content'])
                        print(f"     JSON keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'Not a dict'}")
                        
                        # Look for content in JSON
                        if isinstance(json_data, dict):
                            for key, value in json_data.items():
                                if isinstance(value, str) and len(value) > 100 and any(word in value for word in ['Chương', 'chương']):
                                    print(f"     🎉 Found content in JSON key '{key}'!")
                                    print(f"     Content: {repr(value[:200])}")
                    except:
                        pass
        
        # Check final page state
        print(f"\n🔍 Final page state check...")
        
        final_content = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                if (element) {
                    return {
                        innerHTML: element.innerHTML,
                        textContent: element.textContent,
                        length: element.textContent ? element.textContent.length : 0
                    };
                }
                return null;
            }
        """)
        
        if final_content and final_content['length'] > 0:
            print(f"   ✅ Content found in #chapter-content!")
            print(f"   Length: {final_content['length']}")
            print(f"   Preview: {repr(final_content['textContent'][:200])}")
        else:
            print(f"   ❌ No content in #chapter-content")
        
        # Check for any other content containers
        all_content = await page.evaluate("""
            () => {
                const contentSelectors = [
                    '#chapter-content', '.chapter-content', '.content',
                    '[class*="content"]', '[id*="content"]', 
                    '.break-words', 'main', 'article'
                ];
                
                const results = [];
                
                for (const selector of contentSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        const text = element.textContent || element.innerText;
                        if (text && text.length > 100) {
                            results.push({
                                selector: selector,
                                length: text.length,
                                content: text.substring(0, 200)
                            });
                        }
                    }
                }
                
                return results;
            }
        """)
        
        if all_content:
            print(f"\n📄 Found content in other elements:")
            for content in all_content:
                print(f"   Selector: {content['selector']}")
                print(f"   Length: {content['length']}")
                print(f"   Preview: {repr(content['content'])}")

if __name__ == "__main__":
    asyncio.run(monitor_ajax_requests())
