#!/usr/bin/env python3
"""
Try to decrypt the content using Python
"""

import base64
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import binasci<PERSON>

def try_decrypt_content():
    """Try to decrypt the encrypted content using various methods"""
    
    # The encrypted content from the website
    encrypted_content = "g3+m1UNZgOFW33X3XTdrfKXlh2EMUDDoI/L3b6v3mOIYJ6WyF0w1xMNGZsJU8+AY8UJDpaQ4slR0svVNs7Wm/qZ+tsP0Hn8R8jIZKalAIVeO8cyRcurr0CgXE5bUMjxRWuIhlpFYU0kJcP5+coMk+VD86B4jG/QiYqfvP8iCktvLszIqntUVMf7ByQZ+jkV34NxT75Pw4bg8JFhxD4mZLg9Q+bfNQgH7zfn9Io2iQF32f4HzKtpBM4oa6esUsVIVxBzxNLzrsfDogmqp6B3NPw=="
    
    print("🔐 Python Decryption Attempts")
    print("=" * 50)
    print(f"Encrypted content: {encrypted_content}")
    print(f"Length: {len(encrypted_content)}")
    
    # Step 1: Base64 decode
    try:
        base64_decoded = base64.b64decode(encrypted_content)
        print(f"\n✅ Base64 decoded successfully")
        print(f"Decoded length: {len(base64_decoded)}")
        print(f"Decoded bytes (first 50): {base64_decoded[:50]}")
        print(f"Decoded hex (first 50): {base64_decoded[:50].hex()}")
    except Exception as e:
        print(f"❌ Base64 decode failed: {e}")
        return
    
    # Step 2: Try different decryption methods
    print(f"\n🔧 Trying different decryption methods...")
    
    # Method 1: Try common keys for AES
    common_keys = [
        "metruyencv2024",
        "metruyencv",
        "truyen2024", 
        "chapter",
        "1234567890123456",
        "abcdef1234567890",
        "key123456789012",
        "metruyencvkey123",
        "chapterkey123456",
        "vietnamnovel2024"
    ]
    
    common_ivs = [
        "1234567890123456",
        "abcdef1234567890", 
        "0000000000000000",
        "iv12345678901234",
        "metruyencv123456",
        "chapteriv1234567"
    ]
    
    for key_str in common_keys:
        for iv_str in common_ivs:
            try:
                # Ensure key and IV are 16 bytes
                key = key_str.encode('utf-8')[:16].ljust(16, b'\0')
                iv = iv_str.encode('utf-8')[:16].ljust(16, b'\0')
                
                # Try AES CBC
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(base64_decoded)
                
                # Try to unpad
                try:
                    unpadded = unpad(decrypted, AES.block_size)
                    decrypted_text = unpadded.decode('utf-8')
                except:
                    # If unpadding fails, try without unpadding
                    decrypted_text = decrypted.decode('utf-8', errors='ignore')
                
                # Check if this looks like Vietnamese content
                if len(decrypted_text) > 50 and any(word in decrypted_text for word in ['Chương', 'chương', 'Nhìn', 'nhìn', 'thấy', 'Lâm', 'Bạch']):
                    print(f"\n🎉 SUCCESS with AES CBC!")
                    print(f"Key: {key_str}")
                    print(f"IV: {iv_str}")
                    print(f"Decrypted length: {len(decrypted_text)}")
                    print(f"Content preview: {repr(decrypted_text[:300])}")
                    return decrypted_text
                    
            except Exception as e:
                continue
    
    # Method 2: Try XOR with different keys
    print(f"\n🔧 Trying XOR decryption...")
    
    xor_keys = [123, 255, 42, 7, 13, 88, 99, 111, 222]
    
    for xor_key in xor_keys:
        try:
            decrypted_bytes = bytes([b ^ xor_key for b in base64_decoded])
            decrypted_text = decrypted_bytes.decode('utf-8', errors='ignore')
            
            if len(decrypted_text) > 50 and any(word in decrypted_text for word in ['Chương', 'chương', 'Nhìn', 'nhìn']):
                print(f"\n🎉 SUCCESS with XOR!")
                print(f"XOR key: {xor_key}")
                print(f"Decrypted length: {len(decrypted_text)}")
                print(f"Content preview: {repr(decrypted_text[:300])}")
                return decrypted_text
                
        except Exception as e:
            continue
    
    # Method 3: Try simple character shifting
    print(f"\n🔧 Trying character shifting...")
    
    for shift in range(1, 26):
        try:
            decrypted_bytes = bytes([(b + shift) % 256 for b in base64_decoded])
            decrypted_text = decrypted_bytes.decode('utf-8', errors='ignore')
            
            if len(decrypted_text) > 50 and any(word in decrypted_text for word in ['Chương', 'chương']):
                print(f"\n🎉 SUCCESS with character shifting!")
                print(f"Shift: {shift}")
                print(f"Decrypted length: {len(decrypted_text)}")
                print(f"Content preview: {repr(decrypted_text[:300])}")
                return decrypted_text
                
        except Exception as e:
            continue
    
    # Method 4: Try to interpret as different encodings
    print(f"\n🔧 Trying different encodings...")
    
    encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            decrypted_text = base64_decoded.decode(encoding, errors='ignore')
            
            if len(decrypted_text) > 50 and any(word in decrypted_text for word in ['Chương', 'chương']):
                print(f"\n🎉 SUCCESS with encoding!")
                print(f"Encoding: {encoding}")
                print(f"Decrypted length: {len(decrypted_text)}")
                print(f"Content preview: {repr(decrypted_text[:300])}")
                return decrypted_text
                
        except Exception as e:
            continue
    
    # Method 5: Try reverse operations
    print(f"\n🔧 Trying reverse operations...")
    
    try:
        # Try reversing the bytes
        reversed_bytes = base64_decoded[::-1]
        decrypted_text = reversed_bytes.decode('utf-8', errors='ignore')
        
        if len(decrypted_text) > 50 and any(word in decrypted_text for word in ['Chương', 'chương']):
            print(f"\n🎉 SUCCESS with byte reversal!")
            print(f"Decrypted length: {len(decrypted_text)}")
            print(f"Content preview: {repr(decrypted_text[:300])}")
            return decrypted_text
            
    except Exception as e:
        pass
    
    print(f"\n❌ All decryption methods failed")
    print(f"Raw bytes (first 100): {base64_decoded[:100]}")
    print(f"As UTF-8 (ignore errors): {base64_decoded.decode('utf-8', errors='ignore')[:200]}")
    
    return None

if __name__ == "__main__":
    try:
        result = try_decrypt_content()
        if result:
            print(f"\n🎯 FINAL SUCCESS!")
            print(f"Full content length: {len(result)}")
            print(f"Full content: {result}")
        else:
            print(f"\n🎯 FAILED - Could not decrypt content")
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have pycryptodome installed: pip install pycryptodome")
