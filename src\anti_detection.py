"""
Anti-Detection Manager for bypassing bot detection systems
Implements user agent rotation, delays, proxy support, and stealth techniques
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any
from fake_useragent import UserAgent
from playwright.async_api import BrowserContext, Page
from loguru import logger


class AntiDetectionManager:
    """Manages anti-detection strategies for web scraping"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.ua = UserAgent()
        self.current_user_agent = None
        self.last_request_time = 0
        
        # Load configuration
        self.stealth_config = self.config.get('scraper.stealth', {})
        self.user_agents = self.stealth_config.get('user_agents', [])
        self.delays = self.stealth_config.get('delays', {})
        self.proxy_config = self.stealth_config.get('proxy', {})
    
    async def get_context_options(self) -> Dict[str, Any]:
        """Get browser context options with anti-detection settings"""
        options = {
            'user_agent': self._get_random_user_agent(),
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            'permissions': [],
            'extra_http_headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
        }
        
        # Add proxy if configured
        if self.proxy_config.get('enabled', False):
            proxy = self._get_proxy()
            if proxy:
                options['proxy'] = proxy
        
        return options
    
    def _get_random_user_agent(self) -> str:
        """Get a random user agent from configured list or generate one"""
        if self.user_agents:
            user_agent = random.choice(self.user_agents)
        else:
            try:
                user_agent = self.ua.random
            except Exception:
                # Fallback user agent
                user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        
        self.current_user_agent = user_agent
        logger.debug(f"Selected user agent: {user_agent}")
        return user_agent
    
    def _get_proxy(self) -> Optional[Dict[str, str]]:
        """Get proxy configuration"""
        if not self.proxy_config.get('enabled', False):
            return None
        
        proxy_list = self.proxy_config.get('list', [])
        if not proxy_list:
            return None
        
        if self.proxy_config.get('rotation', False):
            proxy = random.choice(proxy_list)
        else:
            proxy = proxy_list[0]
        
        return {
            'server': proxy.get('server'),
            'username': proxy.get('username'),
            'password': proxy.get('password')
        }
    
    async def apply_stealth_scripts(self, context: BrowserContext) -> None:
        """Apply stealth JavaScript to hide automation"""
        stealth_script = """
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Mock languages and plugins
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // Hide automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        
        // Mock chrome runtime
        if (!window.chrome) {
            window.chrome = {};
        }
        if (!window.chrome.runtime) {
            window.chrome.runtime = {};
        }
        
        // Override toString methods
        const originalToString = Function.prototype.toString;
        Function.prototype.toString = function() {
            if (this === window.navigator.permissions.query) {
                return 'function query() { [native code] }';
            }
            return originalToString.apply(this, arguments);
        };
        """
        
        await context.add_init_script(stealth_script)
        logger.debug("Stealth scripts applied to browser context")
    
    async def randomize_user_agent(self, page: Page) -> None:
        """Randomize user agent for the current page"""
        new_user_agent = self._get_random_user_agent()
        await page.set_extra_http_headers({
            'User-Agent': new_user_agent
        })
        logger.debug(f"User agent updated: {new_user_agent}")
    
    async def add_random_delay(self) -> None:
        """Add random delay between requests"""
        min_delay = self.delays.get('min_delay', 1)
        max_delay = self.delays.get('max_delay', 3)
        
        # Calculate delay based on last request time
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        delay = random.uniform(min_delay, max_delay)
        
        # Ensure minimum delay between requests
        if time_since_last < delay:
            actual_delay = delay - time_since_last
            logger.debug(f"Adding delay: {actual_delay:.2f} seconds")
            await asyncio.sleep(actual_delay)
        
        self.last_request_time = time.time()
    
    async def simulate_human_behavior(self, page: Page) -> None:
        """Simulate human-like behavior on the page"""
        try:
            # Random mouse movements
            await page.mouse.move(
                random.randint(100, 800),
                random.randint(100, 600)
            )
            
            # Random scroll
            scroll_amount = random.randint(100, 500)
            await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            
            # Small delay
            await asyncio.sleep(random.uniform(0.5, 1.5))
            
            # Scroll back up a bit
            await page.evaluate(f"window.scrollBy(0, -{scroll_amount // 2})")
            
            logger.debug("Human behavior simulation completed")
            
        except Exception as e:
            logger.warning(f"Human behavior simulation failed: {e}")
    
    async def handle_captcha_detection(self, page: Page) -> bool:
        """Detect and handle CAPTCHA challenges"""
        captcha_selectors = [
            '.captcha',
            '#captcha',
            '[class*="captcha"]',
            '.recaptcha',
            '#recaptcha',
            '[class*="recaptcha"]',
            '.hcaptcha',
            '#hcaptcha',
            '[class*="hcaptcha"]'
        ]
        
        for selector in captcha_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    logger.warning("CAPTCHA detected on page")
                    return True
            except Exception:
                continue
        
        return False
    
    async def check_rate_limiting(self, page: Page) -> bool:
        """Check if page shows rate limiting or blocking"""
        blocking_indicators = [
            'rate limit',
            'too many requests',
            'blocked',
            'access denied',
            'forbidden',
            'captcha',
            'verify you are human',
            'unusual traffic'
        ]
        
        try:
            page_content = await page.content()
            page_text = page_content.lower()
            
            for indicator in blocking_indicators:
                if indicator in page_text:
                    logger.warning(f"Rate limiting detected: {indicator}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Rate limiting check failed: {e}")
            return False
    
    async def handle_cloudflare_challenge(self, page: Page) -> bool:
        """Handle Cloudflare challenge if detected"""
        try:
            # Check for Cloudflare challenge
            cf_selectors = [
                '.cf-browser-verification',
                '#cf-wrapper',
                '.cf-checking-browser',
                '[data-translate="checking_browser"]'
            ]
            
            for selector in cf_selectors:
                element = await page.query_selector(selector)
                if element:
                    logger.info("Cloudflare challenge detected, waiting...")
                    
                    # Wait for challenge to complete (up to 30 seconds)
                    for _ in range(30):
                        await asyncio.sleep(1)
                        
                        # Check if challenge is completed
                        if not await page.query_selector(selector):
                            logger.info("Cloudflare challenge completed")
                            return True
                    
                    logger.warning("Cloudflare challenge timeout")
                    return False
            
            return True  # No challenge detected
            
        except Exception as e:
            logger.error(f"Cloudflare challenge handling failed: {e}")
            return False
