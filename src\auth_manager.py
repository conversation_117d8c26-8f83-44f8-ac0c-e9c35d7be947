"""
Authentication Manager for MetruyenScraper
Handles login, token management, and authenticated requests
"""

import json
import time
import aiohttp
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class AuthenticationManager:
    """Manages authentication for metruyencv.com"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.auth_config = self.config.get('scraper.authentication', {})
        self.token_data: Optional[Dict[str, Any]] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Load existing token if available
        self._load_token()
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Initialize HTTP session"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            logger.debug("Authentication manager session started")
    
    async def close(self) -> None:
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.debug("Authentication manager session closed")
    
    def is_enabled(self) -> bool:
        """Check if authentication is enabled"""
        return self.auth_config.get('enabled', False)
    
    def has_credentials(self) -> bool:
        """Check if credentials are configured"""
        credentials = self.auth_config.get('credentials', {})
        return bool(credentials.get('email') and credentials.get('password'))
    
    def is_authenticated(self) -> bool:
        """Check if currently authenticated with valid token"""
        if not self.token_data:
            return False
        
        # Check token expiration
        expires_at = self.token_data.get('expires_at', 0)
        return time.time() < expires_at
    
    async def authenticate(self) -> bool:
        """Perform authentication and obtain access token"""
        if not self.is_enabled():
            logger.debug("Authentication is disabled")
            return True
        
        if not self.has_credentials():
            logger.warning("Authentication enabled but no credentials configured")
            return False
        
        if self.is_authenticated():
            logger.debug("Already authenticated with valid token")
            return True
        
        logger.info("🔐 Performing authentication...")
        
        try:
            # Prepare authentication request
            credentials = self.auth_config.get('credentials', {})
            api_endpoint = self.auth_config.get('api_endpoint')
            
            # Get current user agent for device_name
            user_agents = self.config.get('scraper.stealth.user_agents', [])
            device_name = user_agents[0] if user_agents else "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            
            payload = {
                "email": credentials.get('email'),
                "password": credentials.get('password'),
                "remember": credentials.get('remember', 1),
                "device_name": device_name
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': device_name,
                'Origin': 'https://webtruyen.diendantruyen.com/',
                'Referer': 'https://webtruyen.diendantruyen.com/'
            }
            
            logger.debug(f"Authenticating with endpoint: {api_endpoint}")
            logger.debug(f"Email: {credentials.get('email')}")
            
            async with self.session.post(api_endpoint, json=payload, headers=headers) as response:
                response_text = await response.text()
                logger.debug(f"Auth response status: {response.status}")
                logger.debug(f"Auth response: {response_text[:200]}...")
                
                if response.status == 200:
                    data = await response.json()

                    # Extract token from response - metruyencv.com returns it in data.token
                    token = (data.get('access_token') or
                            data.get('token') or
                            (data.get('data', {}).get('token') if isinstance(data.get('data'), dict) else None))

                    if token:
                        # Calculate expiration time (default 24 hours if not provided)
                        expires_in = data.get('expires_in', 86400)  # 24 hours default
                        expires_at = time.time() + expires_in
                        
                        self.token_data = {
                            'access_token': token,
                            'token_type': data.get('token_type', 'Bearer'),
                            'expires_at': expires_at,
                            'user_info': data.get('user', {}),
                            'obtained_at': time.time()
                        }
                        
                        # Save token to file
                        self._save_token()
                        
                        logger.info("✅ Authentication successful")
                        logger.debug(f"Token expires at: {time.ctime(expires_at)}")
                        return True
                    else:
                        logger.error("No access token in response")
                        logger.debug(f"Response data: {data}")
                        return False
                else:
                    logger.error(f"Authentication failed: HTTP {response.status}")
                    logger.error(f"Response: {response_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for requests"""
        if not self.is_authenticated():
            return {}
        
        token_type = self.token_data.get('token_type', 'Bearer')
        access_token = self.token_data.get('access_token')
        
        return {
            'Authorization': f'{token_type} {access_token}'
        }
    
    def get_auth_cookies(self) -> Dict[str, str]:
        """Get authentication cookies for browser requests"""
        if not self.is_authenticated():
            return {}
        
        # Some sites might use cookies instead of headers
        access_token = self.token_data.get('access_token')
        return {
            'auth_token': access_token,
            'access_token': access_token
        }
    
    async def refresh_token_if_needed(self) -> bool:
        """Refresh token if it's about to expire"""
        if not self.auth_config.get('auto_refresh', True):
            return True
        
        if not self.token_data:
            return await self.authenticate()
        
        # Refresh if token expires in less than 1 hour
        expires_at = self.token_data.get('expires_at', 0)
        if time.time() + 3600 > expires_at:
            logger.info("🔄 Token expiring soon, refreshing...")
            return await self.authenticate()
        
        return True
    
    def _load_token(self) -> None:
        """Load token from storage file"""
        token_file = self.auth_config.get('token_storage', 'auth_token.json')
        token_path = Path(token_file)
        
        if token_path.exists():
            try:
                with open(token_path, 'r', encoding='utf-8') as f:
                    self.token_data = json.load(f)
                
                if self.is_authenticated():
                    logger.debug("Loaded valid token from storage")
                else:
                    logger.debug("Loaded expired token from storage")
                    
            except Exception as e:
                logger.warning(f"Failed to load token: {e}")
                self.token_data = None
    
    def _save_token(self) -> None:
        """Save token to storage file"""
        if not self.token_data:
            return
        
        token_file = self.auth_config.get('token_storage', 'auth_token.json')
        token_path = Path(token_file)
        
        try:
            with open(token_path, 'w', encoding='utf-8') as f:
                json.dump(self.token_data, f, indent=2)
            
            logger.debug(f"Token saved to {token_path}")
            
        except Exception as e:
            logger.warning(f"Failed to save token: {e}")
    
    def clear_token(self) -> None:
        """Clear stored token"""
        self.token_data = None
        
        token_file = self.auth_config.get('token_storage', 'auth_token.json')
        token_path = Path(token_file)
        
        if token_path.exists():
            try:
                token_path.unlink()
                logger.debug("Token file deleted")
            except Exception as e:
                logger.warning(f"Failed to delete token file: {e}")
    
    def get_user_info(self) -> Dict[str, Any]:
        """Get authenticated user information"""
        if not self.is_authenticated():
            return {}
        
        return self.token_data.get('user_info', {})
    
    def get_token_info(self) -> Dict[str, Any]:
        """Get token information for debugging"""
        if not self.token_data:
            return {'status': 'no_token'}
        
        expires_at = self.token_data.get('expires_at', 0)
        return {
            'status': 'valid' if self.is_authenticated() else 'expired',
            'expires_at': time.ctime(expires_at),
            'expires_in_seconds': max(0, expires_at - time.time()),
            'user_info': self.token_data.get('user_info', {}),
            'obtained_at': time.ctime(self.token_data.get('obtained_at', 0))
        }
