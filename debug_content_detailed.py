#!/usr/bin/env python3
"""
Detailed debug script for content extraction issues
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def debug_content_detailed():
    """Debug content extraction with very detailed analysis"""
    
    url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    print("🔍 Detailed Content Extraction Debug")
    print("=" * 60)
    print(f"URL: {url}")
    print()
    
    async with MetruyenScraper() as scraper:
        # Get the page directly
        await scraper.start()
        page = scraper.scraper_engine.page
        
        print("🚀 Navigating to page...")
        await page.goto(url, wait_until='networkidle')
        await asyncio.sleep(5)  # Wait for full load
        
        print("📊 Analyzing page state...")
        
        # Check authentication status
        auth_manager = scraper.scraper_engine.auth_manager
        print(f"🔐 Authentication: {'✅ Active' if auth_manager.is_authenticated() else '❌ Not authenticated'}")
        
        # Check page title
        title = await page.title()
        print(f"📄 Page title: {title}")
        
        # Check for chapter data
        chapter_data = await page.evaluate("() => window.chapterData")
        print(f"📋 Chapter data available: {'✅ Yes' if chapter_data else '❌ No'}")
        if chapter_data:
            print(f"   Content field: {'✅ Present' if chapter_data.get('content') else '❌ Missing'}")
            if chapter_data.get('content'):
                print(f"   Content length: {len(chapter_data['content'])} characters")
                print(f"   Content preview: {repr(chapter_data['content'][:100])}")
        
        # Check #chapter-content element
        print(f"\n🔍 Analyzing #chapter-content element...")
        
        element_info = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                if (!element) return { exists: false };
                
                return {
                    exists: true,
                    visible: element.offsetParent !== null,
                    innerHTML: element.innerHTML,
                    textContent: element.textContent,
                    innerText: element.innerText,
                    className: element.className,
                    style: element.style.cssText,
                    computedStyle: {
                        display: getComputedStyle(element).display,
                        visibility: getComputedStyle(element).visibility,
                        opacity: getComputedStyle(element).opacity
                    }
                };
            }
        """)
        
        if element_info['exists']:
            print(f"   ✅ Element exists")
            print(f"   Visible: {'✅ Yes' if element_info['visible'] else '❌ No'}")
            print(f"   Class: {element_info['className']}")
            print(f"   Style: {element_info['style']}")
            print(f"   Computed display: {element_info['computedStyle']['display']}")
            print(f"   Computed visibility: {element_info['computedStyle']['visibility']}")
            print(f"   Computed opacity: {element_info['computedStyle']['opacity']}")
            print(f"   innerHTML length: {len(element_info['innerHTML'])}")
            print(f"   textContent length: {len(element_info['textContent'] or '')}")
            print(f"   innerText length: {len(element_info['innerText'] or '')}")
            
            if element_info['innerHTML']:
                print(f"   innerHTML preview: {repr(element_info['innerHTML'][:200])}")
            if element_info['textContent']:
                print(f"   textContent preview: {repr(element_info['textContent'][:200])}")
            if element_info['innerText']:
                print(f"   innerText preview: {repr(element_info['innerText'][:200])}")
        else:
            print(f"   ❌ Element does not exist")
        
        # Try to find content decryption functions
        print(f"\n🔍 Looking for content decryption functions...")
        
        decryption_info = await page.evaluate("""
            () => {
                const functions = [];
                
                // Check for common decryption function names
                const commonNames = [
                    'decryptContent', 'decodeContent', 'loadContent', 'showContent',
                    'renderContent', 'processContent', 'initContent', 'displayContent'
                ];
                
                for (const name of commonNames) {
                    if (typeof window[name] === 'function') {
                        functions.push(name);
                    }
                }
                
                // Look for any function with 'decrypt', 'decode', or 'content' in the name
                for (const prop in window) {
                    if (typeof window[prop] === 'function') {
                        const lowerProp = prop.toLowerCase();
                        if ((lowerProp.includes('decrypt') || lowerProp.includes('decode') || 
                             lowerProp.includes('content')) && !functions.includes(prop)) {
                            functions.push(prop);
                        }
                    }
                }
                
                return functions;
            }
        """)
        
        print(f"   Found functions: {decryption_info}")
        
        # Try to manually decrypt content if available
        if chapter_data and chapter_data.get('content') and decryption_info:
            print(f"\n🔧 Attempting manual content decryption...")
            
            for func_name in decryption_info[:3]:  # Try first 3 functions
                try:
                    print(f"   Trying function: {func_name}")
                    
                    result = await page.evaluate(f"""
                        async () => {{
                            try {{
                                const result = await window.{func_name}(window.chapterData.content);
                                return {{ success: true, content: result, length: result ? result.length : 0 }};
                            }} catch (e) {{
                                return {{ success: false, error: e.message }};
                            }}
                        }}
                    """)
                    
                    print(f"   Result: {result}")
                    
                    if result.get('success') and result.get('content'):
                        print(f"   ✅ Decryption successful with {func_name}!")
                        print(f"   Content length: {result['length']}")
                        print(f"   Content preview: {repr(result['content'][:200])}")
                        
                        # Try to inject the content
                        await page.evaluate(f"""
                            () => {{
                                const element = document.getElementById('chapter-content');
                                if (element) {{
                                    element.innerHTML = `{result['content'].replace('`', '\\`')}`;
                                }}
                            }}
                        """)
                        
                        print(f"   ✅ Content injected into #chapter-content")
                        break
                        
                except Exception as e:
                    print(f"   ❌ Function {func_name} failed: {e}")
        
        # Final check of #chapter-content after processing
        print(f"\n🔍 Final check of #chapter-content...")
        
        final_content = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                if (!element) return null;
                
                return {
                    innerHTML: element.innerHTML,
                    textContent: element.textContent,
                    innerText: element.innerText
                };
            }
        """)
        
        if final_content:
            print(f"   innerHTML length: {len(final_content['innerHTML'])}")
            print(f"   textContent length: {len(final_content['textContent'] or '')}")
            print(f"   innerText length: {len(final_content['innerText'] or '')}")
            
            if final_content['textContent'] and len(final_content['textContent']) > 100:
                print(f"   ✅ Content found!")
                print(f"   Preview: {repr(final_content['textContent'][:300])}")
            else:
                print(f"   ❌ Still no substantial content")
        
        # Take a screenshot for manual inspection
        screenshot_path = f"debug_detailed_{int(asyncio.get_event_loop().time())}.png"
        await page.screenshot(path=screenshot_path)
        print(f"\n📸 Screenshot saved: {screenshot_path}")
        
        print(f"\n🎯 Summary:")
        print(f"   Authentication: {'✅' if auth_manager.is_authenticated() else '❌'}")
        print(f"   Chapter data: {'✅' if chapter_data else '❌'}")
        print(f"   Element exists: {'✅' if element_info.get('exists') else '❌'}")
        print(f"   Content available: {'✅' if final_content and final_content.get('textContent') and len(final_content['textContent']) > 100 else '❌'}")

if __name__ == "__main__":
    asyncio.run(debug_content_detailed())
