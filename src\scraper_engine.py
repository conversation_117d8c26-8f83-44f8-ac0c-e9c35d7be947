"""
Core Scraper Engine using <PERSON>wright for dynamic content handling
Supports anti-detection measures and robust error handling
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from fake_useragent import UserAgent
from loguru import logger

from .config_manager import ConfigManager
from .anti_detection import AntiDetectionManager
from .data_processor import DataProcessor


class ScraperEngine:
    """Main scraper engine with Playwright browser automation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = ConfigManager(config_path)
        self.anti_detection = AntiDetectionManager(self.config)
        self.data_processor = DataProcessor(self.config)

        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_file = log_config.get('file', 'scraper.log')
        log_format = log_config.get('format', '{time} | {level} | {message}')
        
        logger.add(log_file, level=log_level, format=log_format, rotation="10 MB")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Initialize browser and context"""
        try:

            playwright = await async_playwright().start()

            browser_config = self.config.get('scraper.browser', {})

            # Launch browser with stealth settings
            self.browser = await playwright.chromium.launch(
                headless=browser_config.get('headless', True),
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',  # Speed optimization
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection',
                ]
            )

            # Create context with anti-detection settings
            context_options = await self.anti_detection.get_context_options()
            self.context = await self.browser.new_context(**context_options)

            # Apply stealth scripts
            await self.anti_detection.apply_stealth_scripts(self.context)

            # Create page
            self.page = await self.context.new_page()

            # Viewport is set in context options, no need to set it again on page

            logger.info("Scraper engine initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize scraper engine: {e}")
            raise
    
    async def close(self) -> None:
        """Close browser and cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()

            logger.info("Scraper engine closed successfully")

        except Exception as e:
            logger.error(f"Error closing scraper engine: {e}")

    async def scrape_chapters(self, url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Scrape a single URL and extract data"""
        if not self.page:
            raise RuntimeError("Scraper engine not initialized. Use async context manager.")
        
        try:
            logger.info(f"Scraping URL: {url}")

            # Apply anti-detection measures
            await self.anti_detection.randomize_user_agent(self.page)
            await self.anti_detection.add_random_delay()
            
            # Navigate to URL
            timeout = self.config.get('scraper.browser.timeout', 30000)
            response = await self.page.goto(url, timeout=timeout, wait_until='networkidle')
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for dynamic content
            await self._wait_for_content(target_name)
            
            # Extract data
            data = await self._extract_data_list(url, target_name)
            
            logger.info(f"Successfully scraped: {url}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            raise

    async def scrape_url(self, url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Scrape a single URL and extract data"""
        if not self.page:
            raise RuntimeError("Scraper engine not initialized. Use async context manager.")
        
        try:
            logger.info(f"Scraping URL: {url}")

            # Apply anti-detection measures
            await self.anti_detection.randomize_user_agent(self.page)
            await self.anti_detection.add_random_delay()
            
            # Navigate to URL
            timeout = self.config.get('scraper.browser.timeout', 30000)
            response = await self.page.goto(url, timeout=timeout, wait_until='networkidle')
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for dynamic content
            await self._wait_for_content(target_name)
            
            # Extract data
            data = await self._extract_data(url, target_name)
            
            logger.info(f"Successfully scraped: {url}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            raise
    
    async def _wait_for_content(self, target_name: str) -> None:
        """Wait for dynamic content to load"""
        target_config = self.config.get_target_config(target_name)
        wait_conditions = target_config.get('wait_conditions', ['networkidle'])

        # Wait for specific selectors if configured
        selectors = self.config.get_selectors(target_name)

        logger.info(f"🔍 Waiting for content with selectors: {selectors}")

        try:
            # Wait for main content selector
            if 'content' in selectors:
                logger.info(f"⏳ Waiting for content selector: {selectors['content']}")
                await self.page.wait_for_selector(selectors['content'], timeout=10000)
                logger.info("✅ Content selector found")

            # Additional wait for page stability
            delays = self.config.get_delays()
            page_load_delay = delays.get('page_load_delay', 3)
            logger.info(f"⏳ Additional wait for page stability: {page_load_delay}s")
            await asyncio.sleep(page_load_delay)

        except Exception as e:
            logger.warning(f"Content wait timeout: {e}")

        # Debug: Log page structure
        await self._debug_page_structure(selectors)

    async def _debug_page_structure(self, selectors: Dict[str, str]) -> None:
        """Debug page structure and content"""
        try:
            logger.info("🔍 DEBUG: Analyzing page structure...")

            # Get page title
            title = await self.page.title()
            logger.info(f"📄 Page title: {title}")

            # Check if page is fully loaded
            ready_state = await self.page.evaluate("document.readyState")
            logger.info(f"📊 Document ready state: {ready_state}")

            # Log all elements matching content selectors
            content_selector = selectors.get('content', '')
            if content_selector:
                logger.info(f"🔍 Searching for content with selector: {content_selector}")

                # Split selector by comma and test each part
                selector_parts = [s.strip() for s in content_selector.split(',')]
                for i, selector in enumerate(selector_parts):
                    logger.info(f"  Testing selector {i+1}: {selector}")
                    try:
                        elements = await self.page.query_selector_all(selector)
                        logger.info(f"    Found {len(elements)} elements")

                        for j, element in enumerate(elements[:3]):  # Log first 3 elements
                            try:
                                tag_name = await element.evaluate("el => el.tagName")
                                class_name = await element.get_attribute("class") or "no-class"
                                id_attr = await element.get_attribute("id") or "no-id"
                                is_visible = await element.is_visible()
                                text_content = await element.text_content()
                                text_preview = (text_content[:100] + "...") if text_content and len(text_content) > 100 else text_content

                                logger.info(f"      Element {j+1}: <{tag_name}> class='{class_name}' id='{id_attr}' visible={is_visible}")
                                logger.info(f"        Text preview: {repr(text_preview)}")

                            except Exception as e:
                                logger.warning(f"      Error analyzing element {j+1}: {e}")

                    except Exception as e:
                        logger.warning(f"    Error with selector '{selector}': {e}")

            # Check for locked content indicators
            locked_selector = selectors.get('locked_content', '')
            if locked_selector:
                logger.info(f"🔒 Checking for locked content with selector: {locked_selector}")
                locked_elements = await self.page.query_selector_all(locked_selector)
                logger.info(f"    Found {len(locked_elements)} locked content indicators")

                for i, element in enumerate(locked_elements[:2]):
                    try:
                        text = await element.text_content()
                        logger.info(f"      Locked indicator {i+1}: {repr(text[:100])}")
                    except Exception:
                        pass

            # Take screenshot for debugging
            screenshot_path = f"screenshots/debug/debug_screenshot_{int(time.time())}.png"
            await self.page.screenshot(path=screenshot_path)
            logger.info(f"📸 Debug screenshot saved: {screenshot_path}")

        except Exception as e:
            logger.error(f"Debug page structure failed: {e}")

    async def _extract_data(self, url: str, target_name: str) -> Dict[str, Any]:
        """Extract data from current page"""
        selectors = self.config.get_selectors(target_name)
        data = {
            'url': url,
            'timestamp': time.time(),
            'title': None,
            'content': None,
            'metadata': {},
            'navigation': {},
            'is_locked': False
        }
        
        try:
            # Extract title
            if 'title' in selectors:
                title_element = await self.page.query_selector(selectors['title'])
                if title_element:
                    data['title'] = await title_element.inner_text()
            
            # Check if content is locked
            logger.info("🔒 Checking for locked content...")
            if 'locked_content' in selectors:
                locked_element = await self.page.query_selector(selectors['locked_content'])
                data['is_locked'] = locked_element is not None
                logger.info(f"🔒 Locked content detected: {data['is_locked']}")
                if locked_element:
                    try:
                        locked_text = await locked_element.text_content()
                        logger.info(f"🔒 Locked content text: {repr(locked_text[:200])}")
                    except Exception:
                        pass
            else:
                logger.info("🔒 No locked content selector configured")

            # Extract main content
            logger.info("📝 Extracting main content...")
            if 'content' in selectors:
                logger.info(f"📝 Using content selector: {selectors['content']}")

                # Try each selector part separately
                selector_parts = [s.strip() for s in selectors['content'].split(',')]
                content_found = False

                for i, selector in enumerate(selector_parts):
                    logger.info(f"📝 Trying selector {i+1}: {selector}")
                    try:
                        content_elements = await self.page.query_selector_all(selector)
                        logger.info(f"📝 Found {len(content_elements)} elements with selector: {selector}")

                        for j, element in enumerate(content_elements):
                            try:
                                # Check element properties
                                is_visible = await element.is_visible()
                                tag_name = await element.evaluate("el => el.tagName")
                                class_name = await element.get_attribute("class") or ""
                                id_attr = await element.get_attribute("id") or ""

                                logger.info(f"📝   Element {j+1}: <{tag_name}> class='{class_name}' id='{id_attr}' visible={is_visible}")

                                # Try different methods to get content
                                content_methods = [
                                    ("inner_text", lambda el: el.inner_text()),
                                    ("text_content", lambda el: el.text_content()),
                                    ("inner_html", lambda el: el.inner_html())
                                ]

                                for method_name, method in content_methods:
                                    try:
                                        content = await method(element)
                                        if content and content.strip():
                                            logger.info(f"📝   {method_name} content length: {len(content)}")
                                            logger.info(f"📝   {method_name} preview: {repr(content[:200])}")

                                            if not data['content'] and len(content.strip()) > 50:  # Use substantial content
                                                data['content'] = content.strip()
                                                content_found = True
                                                logger.info(f"✅ Content extracted using {method_name}")
                                        else:
                                            logger.info(f"📝   {method_name}: empty or whitespace only")
                                    except Exception as e:
                                        logger.warning(f"📝   {method_name} failed: {e}")

                                # If element is hidden but has content, try to extract it anyway
                                if not content_found and not is_visible:
                                    try:
                                        # Force extract content from hidden element using JavaScript
                                        js_content = await element.evaluate("""
                                            el => {
                                                // Try multiple methods to get content
                                                let content = el.textContent || el.innerText;

                                                // If still empty, try innerHTML and strip HTML tags
                                                if (!content || content.trim().length === 0) {
                                                    const innerHTML = el.innerHTML;
                                                    if (innerHTML) {
                                                        // Create a temporary div to strip HTML tags
                                                        const tempDiv = document.createElement('div');
                                                        tempDiv.innerHTML = innerHTML;
                                                        content = tempDiv.textContent || tempDiv.innerText || '';
                                                    }
                                                }

                                                return content;
                                            }
                                        """)

                                        if js_content and len(js_content.strip()) > 50:
                                            logger.info(f"📝   Hidden element JS extraction: {len(js_content)} chars")
                                            logger.info(f"📝   Hidden content preview: {repr(js_content[:200])}")
                                            data['content'] = js_content.strip()
                                            content_found = True
                                            logger.info(f"✅ Content extracted from hidden element using JavaScript")
                                    except Exception as e:
                                        logger.warning(f"📝   Hidden element extraction failed: {e}")

                                # Special handling for #chapter-content element
                                if not content_found and selector == "#chapter-content":
                                    try:
                                        logger.info("📝   Trying special #chapter-content extraction...")

                                        # Wait a bit more for content to load
                                        await asyncio.sleep(2)

                                        # Try to get innerHTML and process it
                                        innerHTML_content = await element.evaluate("""
                                            el => {
                                                const innerHTML = el.innerHTML;
                                                if (innerHTML && innerHTML.trim().length > 0) {
                                                    // Create a temporary div to process HTML content
                                                    const tempDiv = document.createElement('div');
                                                    tempDiv.innerHTML = innerHTML;

                                                    // Remove script tags and other unwanted elements
                                                    const scripts = tempDiv.querySelectorAll('script, style, canvas');
                                                    scripts.forEach(script => script.remove());

                                                    // Get text content
                                                    let textContent = tempDiv.textContent || tempDiv.innerText || '';

                                                    // Clean up the text
                                                    textContent = textContent.replace(/\\s+/g, ' ').trim();

                                                    return textContent;
                                                }
                                                return '';
                                            }
                                        """)

                                        if innerHTML_content and len(innerHTML_content.strip()) > 50:
                                            logger.info(f"📝   #chapter-content innerHTML extraction: {len(innerHTML_content)} chars")
                                            logger.info(f"📝   innerHTML content preview: {repr(innerHTML_content[:200])}")
                                            data['content'] = innerHTML_content.strip()
                                            content_found = True
                                            logger.info(f"✅ Content extracted from #chapter-content innerHTML")

                                    except Exception as e:
                                        logger.warning(f"📝   #chapter-content special extraction failed: {e}")

                            except Exception as e:
                                logger.warning(f"📝   Error analyzing element {j+1}: {e}")

                        if content_found:
                            break

                    except Exception as e:
                        logger.warning(f"📝 Selector '{selector}' failed: {e}")

                if not content_found:
                    logger.warning("📝 No content extracted from any selector")

                    # Try alternative approaches
                    await self._try_alternative_content_extraction(data)

            else:
                logger.warning("📝 No content selector configured")

            # Extract navigation links
            await self._extract_navigation(data, selectors)

            # Extract metadata
            data['metadata'] = await self._extract_metadata()

            return data

        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise

    async def _extract_data_list(self, url: str, target_name: str) -> Dict[str, Any]:
        """Extract data from current page"""
        selectors = self.config.get_selectors(target_name)
        data = {
            'url': url,
            'timestamp': time.time(),
            'metadata': {},
            'chapters': {},
            'is_locked': False
        }
        
        try:
            if 'chapter_list' in selectors:
                # get chapter list tab html
                chapter_list = await self.page.query_selector(selectors['chapter_list'])
                if chapter_list:
                    # get chapter link 
                    chapter_links = await chapter_list.query_selector_all(selectors['chapter_link'])
                    for link in chapter_links:
                        href = await link.get_attribute('href')
                        if href:
                            data['chapters'][url] = await link.inner_text()
                            logger.info(f"Navigation link found: {href}")
                        else :
                            logger.warning(f"Navigation link found but no href: {link}")
            else:
                logger.warning("No chapter list selector configured")

            # Extract metadata
            data['metadata'] = await self._extract_metadata()

            return data

        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise

    async def _try_alternative_content_extraction(self, data: Dict[str, Any]) -> None:
        """Try alternative methods to extract content"""
        logger.info("🔄 Trying alternative content extraction methods...")

        # Common selectors for Vietnamese novel sites
        alternative_selectors = [
            "#chapter-content",
            ".chapter-content",
            ".content",
            "[id*='content']",
            "[class*='content']",
            ".reading-content",
            ".chapter-text",
            ".story-content",
            "div[id*='chapter']",
            "div[class*='chapter']",
            ".break-words",  # Common class on webtruyen
            "main",
            "article"
        ]

        for selector in alternative_selectors:
            try:
                logger.info(f"🔄 Trying alternative selector: {selector}")
                elements = await self.page.query_selector_all(selector)

                for element in elements:
                    try:
                        # Check if element has substantial text content
                        text_content = await element.text_content()
                        if text_content and len(text_content.strip()) > 100:
                            logger.info(f"🔄 Found content with {selector}: {len(text_content)} chars")
                            logger.info(f"🔄 Content preview: {repr(text_content[:200])}")

                            # Check if it's not navigation or metadata
                            if not any(keyword in text_content.lower() for keyword in
                                     ['chương trước', 'chương sau', 'mục lục', 'đánh giá', 'bình luận']):
                                data['content'] = text_content.strip()
                                logger.info(f"✅ Alternative content extraction successful with: {selector}")
                                return

                    except Exception as e:
                        logger.warning(f"🔄 Error with element: {e}")

            except Exception as e:
                logger.warning(f"🔄 Alternative selector '{selector}' failed: {e}")

        # Try JavaScript execution to get content
        await self._try_javascript_content_extraction(data)

    async def _try_javascript_content_extraction(self, data: Dict[str, Any]) -> None:
        """Try JavaScript-based content extraction"""
        logger.info("🔄 Trying JavaScript content extraction...")

        js_scripts = [
            # Try to find the main content area
            """
            (() => {
                const contentSelectors = [
                    '#chapter-content', '.chapter-content', '.content',
                    '[id*="content"]', '[class*="content"]', '.break-words'
                ];

                for (const selector of contentSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const el of elements) {
                        const text = el.textContent || el.innerText;
                        if (text && text.trim().length > 100) {
                            return {
                                selector: selector,
                                content: text.trim(),
                                length: text.trim().length
                            };
                        }
                    }
                }
                return null;
            })()
            """,

            # Try to get all text content and filter
            """
            (() => {
                const allText = document.body.textContent || document.body.innerText;
                if (allText && allText.length > 500) {
                    return {
                        selector: 'body',
                        content: allText.trim(),
                        length: allText.trim().length
                    };
                }
                return null;
            })()
            """
        ]

        for i, script in enumerate(js_scripts):
            try:
                logger.info(f"🔄 Executing JavaScript method {i+1}")
                result = await self.page.evaluate(script)

                if result and result.get('content'):
                    logger.info(f"🔄 JS extraction found content: {result['length']} chars with {result['selector']}")
                    logger.info(f"🔄 JS content preview: {repr(result['content'][:200])}")

                    if not data['content']:
                        data['content'] = result['content']
                        logger.info(f"✅ JavaScript content extraction successful")
                        return

            except Exception as e:
                logger.warning(f"🔄 JavaScript method {i+1} failed: {e}")
    
    async def _extract_navigation(self, data: Dict[str, Any], selectors: Dict[str, str]) -> None:
        """Extract navigation links"""
        nav_data = {}

        try:
            # Next chapter link - try multiple approaches
            if 'next_chapter' in selectors:
                try:
                    next_element = await self.page.query_selector(selectors['next_chapter'])
                    if next_element:
                        next_href = await next_element.get_attribute('href')
                        if next_href:
                            nav_data['next_chapter'] = urljoin(data['url'], next_href)
                except Exception:
                    # Fallback: look for any link containing "sau" or "next"
                    next_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in next_elements:
                        text = await element.inner_text()
                        if 'sau' in text.lower() or 'next' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['next_chapter'] = urljoin(data['url'], href)
                                break

            # Previous chapter link - try multiple approaches
            if 'prev_chapter' in selectors:
                try:
                    prev_element = await self.page.query_selector(selectors['prev_chapter'])
                    if prev_element:
                        prev_href = await prev_element.get_attribute('href')
                        if prev_href:
                            nav_data['prev_chapter'] = urljoin(data['url'], prev_href)
                except Exception:
                    # Fallback: look for any link containing "trước" or "prev"
                    prev_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in prev_elements:
                        text = await element.inner_text()
                        if 'trước' in text.lower() or 'prev' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['prev_chapter'] = urljoin(data['url'], href)
                                break

        except Exception as e:
            logger.warning(f"Navigation extraction failed: {e}")

        data['navigation'] = nav_data
    
    async def _extract_metadata(self) -> Dict[str, Any]:
        """Extract page metadata"""
        metadata = {}
        
        try:
            # Page title
            title = await self.page.title()
            metadata['page_title'] = title
            
            # Meta description
            desc_element = await self.page.query_selector('meta[name="description"]')
            if desc_element:
                metadata['description'] = await desc_element.get_attribute('content')
            
            # Meta keywords
            keywords_element = await self.page.query_selector('meta[name="keywords"]')
            if keywords_element:
                metadata['keywords'] = await keywords_element.get_attribute('content')
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Metadata extraction failed: {e}")
            return metadata
