#!/usr/bin/env python3
"""
Find the exact decryption method used by metruyencv.com
"""

import asyncio
import sys
import base64
import re
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def find_decryption_method():
    """Find the exact decryption method"""
    
    url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    print("🔍 Finding Decryption Method")
    print("=" * 50)
    
    async with MetruyenScraper() as scraper:
        await scraper.start()
        page = scraper.scraper_engine.page
        
        print("🚀 Loading page...")
        await page.goto(url, wait_until='networkidle')
        await asyncio.sleep(5)
        
        # Get all script content
        print("📜 Analyzing all scripts on the page...")
        
        scripts_content = await page.evaluate("""
            () => {
                const scripts = Array.from(document.querySelectorAll('script'));
                const allScripts = [];
                
                for (const script of scripts) {
                    const content = script.textContent || script.innerHTML;
                    if (content && content.trim().length > 0) {
                        allScripts.push({
                            src: script.src || 'inline',
                            content: content,
                            hasChapterData: content.includes('chapterData'),
                            hasDecrypt: content.toLowerCase().includes('decrypt'),
                            hasDecode: content.toLowerCase().includes('decode'),
                            hasAtob: content.includes('atob'),
                            hasBtoa: content.includes('btoa'),
                            hasCrypto: content.toLowerCase().includes('crypto'),
                            hasAES: content.includes('AES'),
                            hasKey: content.includes('key'),
                            hasIV: content.includes('iv') || content.includes('IV')
                        });
                    }
                }
                
                return allScripts;
            }
        """)
        
        print(f"Found {len(scripts_content)} scripts")
        
        # Look for decryption patterns
        decryption_patterns = []
        
        for i, script in enumerate(scripts_content):
            print(f"\n📄 Script {i+1}:")
            print(f"   Source: {script['src']}")
            print(f"   Length: {len(script['content'])}")
            print(f"   Has chapterData: {script['hasChapterData']}")
            print(f"   Has decrypt: {script['hasDecrypt']}")
            print(f"   Has decode: {script['hasDecode']}")
            print(f"   Has atob: {script['hasAtob']}")
            print(f"   Has crypto: {script['hasCrypto']}")
            print(f"   Has AES: {script['hasAES']}")
            print(f"   Has key/IV: {script['hasKey']} / {script['hasIV']}")
            
            # Look for specific patterns
            content = script['content']
            
            # Pattern 1: Direct decryption function calls
            decrypt_calls = re.findall(r'(\w+)\s*\(\s*["\']?chapterData\.content["\']?\s*\)', content, re.IGNORECASE)
            if decrypt_calls:
                print(f"   🔑 Found decrypt calls: {decrypt_calls}")
                decryption_patterns.extend(decrypt_calls)
            
            # Pattern 2: Function definitions that might decrypt
            func_defs = re.findall(r'function\s+(\w*decrypt\w*|decode\w*)\s*\(', content, re.IGNORECASE)
            if func_defs:
                print(f"   🔑 Found decrypt functions: {func_defs}")
                decryption_patterns.extend(func_defs)
            
            # Pattern 3: Variable assignments with decryption
            var_assigns = re.findall(r'(\w+)\s*=\s*(\w+)\s*\(\s*["\']?chapterData\.content["\']?\s*\)', content)
            if var_assigns:
                print(f"   🔑 Found decrypt assignments: {var_assigns}")
                decryption_patterns.extend([assign[1] for assign in var_assigns])
            
            # Pattern 4: Look for specific crypto libraries usage
            if 'CryptoJS' in content:
                crypto_patterns = re.findall(r'CryptoJS\.(\w+)\.(\w+)', content)
                if crypto_patterns:
                    print(f"   🔑 Found CryptoJS usage: {crypto_patterns}")
            
            # Pattern 5: Look for key/IV definitions
            if script['hasKey'] or script['hasIV']:
                key_patterns = re.findall(r'(?:key|iv)\s*[:=]\s*["\']([^"\']+)["\']', content, re.IGNORECASE)
                if key_patterns:
                    print(f"   🔑 Found keys/IVs: {key_patterns}")
            
            # Show relevant code snippets
            if any([script['hasDecrypt'], script['hasDecode'], script['hasChapterData'], script['hasCrypto']]):
                lines = content.split('\n')
                relevant_lines = []
                for j, line in enumerate(lines):
                    if any(keyword in line.lower() for keyword in ['decrypt', 'decode', 'chapterdata', 'crypto', 'aes']):
                        start = max(0, j-2)
                        end = min(len(lines), j+3)
                        relevant_lines.extend(lines[start:end])
                
                if relevant_lines:
                    print(f"   📝 Relevant code:")
                    for line in relevant_lines[:10]:  # Show first 10 relevant lines
                        print(f"      {line.strip()}")
        
        # Try to execute found decryption patterns
        print(f"\n🔧 Testing found decryption patterns...")
        
        unique_patterns = list(set(decryption_patterns))
        print(f"Unique patterns to test: {unique_patterns}")
        
        for pattern in unique_patterns:
            try:
                print(f"\n   Testing pattern: {pattern}")
                
                result = await page.evaluate(f"""
                    () => {{
                        try {{
                            if (typeof window.{pattern} === 'function' && window.chapterData && window.chapterData.content) {{
                                const result = window.{pattern}(window.chapterData.content);
                                return {{
                                    success: true,
                                    content: result,
                                    length: result ? result.length : 0,
                                    type: typeof result
                                }};
                            }} else {{
                                return {{ success: false, reason: 'Function not found or no chapterData' }};
                            }}
                        }} catch (e) {{
                            return {{ success: false, error: e.message }};
                        }}
                    }}
                """)
                
                print(f"   Result: {result}")
                
                if result.get('success') and result.get('content'):
                    content = result['content']
                    if isinstance(content, str) and len(content) > 50:
                        print(f"   ✅ Pattern successful!")
                        print(f"   Content preview: {repr(content[:200])}")
                        
                        # Check if this looks like Vietnamese text
                        if any(char in content for char in ['Chương', 'chương', 'Nhìn', 'nhìn', 'thấy', 'Lâm', 'Bạch']):
                            print(f"   🎉 This appears to be the correct decryption!")
                            return pattern, content
                
            except Exception as e:
                print(f"   ❌ Pattern failed: {e}")
        
        # If no patterns worked, try manual inspection of the page
        print(f"\n🔍 Manual inspection of page state...")
        
        # Check if content was already decrypted by the page
        final_content = await page.evaluate("""
            () => {
                const element = document.getElementById('chapter-content');
                if (element) {
                    return {
                        innerHTML: element.innerHTML,
                        textContent: element.textContent,
                        innerText: element.innerText,
                        visible: element.offsetParent !== null
                    };
                }
                return null;
            }
        """)
        
        if final_content:
            print(f"   Element state:")
            print(f"     Visible: {final_content['visible']}")
            print(f"     innerHTML length: {len(final_content['innerHTML'])}")
            print(f"     textContent length: {len(final_content['textContent'] or '')}")
            print(f"     innerText length: {len(final_content['innerText'] or '')}")
            
            if final_content['textContent'] and len(final_content['textContent']) > 100:
                print(f"     Content preview: {repr(final_content['textContent'][:200])}")
        
        return None, None

if __name__ == "__main__":
    pattern, content = asyncio.run(find_decryption_method())
    if pattern:
        print(f"\n🎯 SUCCESS: Found working decryption pattern: {pattern}")
        print(f"Content length: {len(content)}")
        print(f"Content preview: {repr(content[:300])}")
    else:
        print(f"\n❌ No working decryption pattern found")
