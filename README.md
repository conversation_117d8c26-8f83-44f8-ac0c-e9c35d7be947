# MetruyenScraper

A powerful and configurable web scraper for websites like webtruyen.diendantruyen.com, designed to handle dynamic content and anti-bot measures.

## 🌟 Key Features

- **Handles Dynamic Websites**: Uses a real browser engine (Playwright) to render JavaScript and load dynamic content.
- **Bypasses Bot Detection**: Employs strategies like user-agent rotation and intelligent delays to avoid getting blocked.
- **Highly Configurable**: Easily adapt the scraper for different websites through a simple `config.yaml` file.
- **Robust and Resilient**: Automatically retries failed requests and handles common scraping errors.
- **Multiple Export Formats**: Save your data as JSON, CSV, or Excel files.

---

## 🚀 Getting Started

### Prerequisites

- **Python 3.10, 3.11, or 3.12**. It is strongly recommended to use one of these versions to avoid installation issues. Python 3.13 and newer may cause errors when installing dependencies.
- **Git**

### Installation

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/MetruyenScraper.git
    cd MetruyenScraper
    ```

2.  **Create and activate a virtual environment:**
    This keeps your project dependencies isolated.
    ```bash
    # Replace 'python' with 'py -3.12' or your specific Python command if needed
    python -m venv venv
    ```
    -   **On Windows:**
        ```bash
        venv\Scripts\activate
        ```
    -   **On macOS/Linux:**
        ```bash
        source venv/bin/activate
        ```

3.  **Install required packages:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Install Playwright browsers:**
    This downloads the necessary browser binaries.
    ```bash
    playwright install chromium
    ```

### Quick Start

Here's a simple example to scrape a single chapter. You can run this by saving it as a Python file (e.g., `run.py`).

```python
import asyncio
from src.metruyenscraper import MetruyenScraper

async def main():
    # The scraper is best used with an 'async with' block
    # to ensure resources are properly managed.
    async with MetruyenScraper() as scraper:
        url = "https://webtruyen.diendantruyen.com/truyen/example/chuong-1" # Example URL
        
        print(f"Scraping URL: {url}")
        data = await scraper.scrape_url(url)
        
        if data:
            print(f"Successfully scraped title: {data['title']}")
            # print(f"Content: {data['content'][:100]}...") # Uncomment to see content
        
        # Export all scraped data to the format specified in config.yaml
        exported_files = scraper.export_data()
        print(f"Data exported to: {exported_files}")

# Run the main asynchronous function
if __name__ == "__main__":
    asyncio.run(main())
```

---

## ⚙️ Configuration (`config.yaml`)

The scraper's behavior is controlled by `config.yaml`. Here are some key settings:

<details>
<summary><strong>Browser Settings</strong></summary>

```yaml
scraper:
  browser:
    headless: true      # Set to false to see the browser UI
    timeout: 30000      # Page load timeout in milliseconds
    viewport:
      width: 1920
      height: 1080
```
</details>

<details>
<summary><strong>Anti-Detection Settings</strong></summary>

```yaml
scraper:
  stealth:
    user_agents:
      - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) ..."
    delays:
      min_delay: 2  # Minimum delay between requests (seconds)
      max_delay: 5  # Maximum delay
```
</details>

<details>
<summary><strong>Target Website Selectors</strong></summary>

You need to tell the scraper where to find the data on the page using CSS selectors.

```yaml
targets:
  webtruyen:
    base_url: "https://webtruyen.diendantruyen.com/"
    selectors:
      title: "h1, .title"                 # Selector for the chapter title
      content: ".content, .chapter-content" # Selector for the main content
      next_chapter: "a:contains('Chương sau')" # Selector for the "next chapter" link
```
</details>

<details>
<summary><strong>Command-Line Usage</strong></summary>

The `run_scraper.py` script provides a convenient command-line interface to perform common scraping tasks.

**Scrape a Single URL:**
```bash
python run_scraper.py single "https://webtruyen.diendantruyen.com/truyen/example/chuong-1"
```

**Scrape Multiple URLs from a File:**

First, create a file named `urls.txt` containing a list of URLs to scrape (one per line):
```
https://webtruyen.diendantruyen.com/truyen/example/chuong-1
https://webtruyen.diendantruyen.com/truyen/example/chuong-2
```

Then run the command:
```bash
python run_scraper.py multiple urls.txt --max-concurrent 2
```

**Scrape All Chapters of a Story:**
```bash
python run_scraper.py story "https://webtruyen.diendantruyen.com/truyen/example-story" --max-chapters 10
```

**Test Site Scraping:**

This command tests if the scraper can successfully extract data from a given URL without actually saving it.
```bash
python run_scraper.py test "https://webtruyen.diendantruyen.com/truyen/example/chuong-1"
```

</details>

---

## 📚 Advanced Usage

<details>
<summary><strong>Scraping Multiple URLs Concurrently</strong></summary>

```python
async def scrape_multiple():
    urls = [
        "https://webtruyen.diendantruyen.com/truyen/example/chuong-1",
        "https://webtruyen.diendantruyen.com/truyen/example/chuong-2",
    ]
    
    async with MetruyenScraper() as scraper:
        # 'max_concurrent' controls how many pages are scraped at the same time.
        results = await scraper.scrape_urls(urls, max_concurrent=2)
        print(f"Scraped {len(results)} URLs successfully")
```
</details>

<details>
<summary><strong>Scraping All Chapters of a Story</strong></summary>

```python
async def scrape_story():
    story_url = "https://webtruyen.diendantruyen.com/truyen/example-story"
    
    async with MetruyenScraper() as scraper:
        # Scrape all chapters, or limit with 'max_chapters'.
        # The scraper will follow the "next chapter" link automatically.
        results = await scraper.scrape_story_chapters(story_url, max_chapters=10)
        print(f"Scraped {len(results)} chapters")
```
</details>

<details>
<summary><strong>Error Handling and Statistics</strong></summary>

The scraper keeps track of successes and failures.

```python
async with MetruyenScraper() as scraper:
    # ... perform scraping ...
    
    stats = scraper.get_statistics()
    print(f"Total items scraped: {stats['data_statistics']['total_items']}")
    print(f"Total errors: {stats['error_statistics']['total_errors']}")
    
    # Get a list of URLs that failed to scrape
    failed_urls = stats['failed_urls']
    for failed in failed_urls:
        print(f"Failed: {failed['url']} - Reason: {failed['error_type']}")
```
</details>

---

## 🔧 Troubleshooting

- **Installation Errors**: Make sure you are using a compatible Python version (3.10-3.12). This is the most common issue.
- **Timeout Errors**: The website is slow to load. Increase the `timeout` value in `config.yaml`.
- **Blocked or CAPTCHA**: The website has detected the scraper. Try increasing the `min_delay` and `max_delay` values in `config.yaml`, or configure proxies.
- **Incorrect Data Scraped**: The CSS selectors in `config.yaml` are likely incorrect for your target website. You will need to inspect the website's HTML to find the correct ones.

---

## ⚖️ Legal

This project is for educational purposes only. Please respect the website's terms of service and `robots.txt`. Be responsible and do not overload website servers.

## 🤝 Contributing

Contributions are welcome! Please fork the repository and submit a pull request.
