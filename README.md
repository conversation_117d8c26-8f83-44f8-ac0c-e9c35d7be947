# MetruyenScraper

A powerful, configurable web scraping solution designed to handle dynamic content and bypass anti-bot detection systems. Built specifically for scraping websites like metruyencv.com that use JavaScript rendering and content protection mechanisms.

## Features

### 🚀 Core Capabilities
- **Dynamic Content Handling**: Uses Playwright for JavaScript rendering and AJAX content loading
- **Anti-Bot Detection Bypass**: Implements multiple strategies to avoid detection
- **Configurable Architecture**: Flexible configuration system for different target websites
- **Robust Error Handling**: Comprehensive retry mechanisms and error recovery
- **Multiple Export Formats**: JSON, CSV, and Excel export support

### 🛡️ Anti-Detection Features
- **User Agent Rotation**: Randomized user agents from configurable pool
- **Request Delays**: Intelligent throttling with random delays
- **Stealth Mode**: Browser fingerprint masking and automation detection bypass
- **Proxy Support**: Configurable proxy rotation (optional)
- **Human Behavior Simulation**: Mouse movements and scrolling patterns

### 📊 Data Processing
- **Data Validation**: Automatic data cleaning and validation
- **Multiple Formats**: Export to JSON, CSV, Excel
- **Statistics Tracking**: Comprehensive scraping statistics and error reporting
- **Caching System**: Efficient data caching and batch processing

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd MetruyenScraper
```

2. **Create and activate a virtual environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
```

3. **Install Python dependencies**:
```bash
pip install -r requirements.txt
```

4. **Install Playwright browsers**:
```bash
playwright install chromium
```

## Quick Start

### Basic Usage

```python
import asyncio
from src.metruyenscraper import MetruyenScraper

async def main():
    async with MetruyenScraper() as scraper:
        # Scrape a single URL
        url = "https://metruyencv.com/truyen/example/chuong-1"
        data = await scraper.scrape_url(url)
        
        if data:
            print(f"Title: {data['title']}")
            print(f"Content: {data['content'][:100]}...")
        
        # Export data
        exported_files = scraper.export_data()
        print(f"Data exported to: {exported_files}")

asyncio.run(main())
```

### Scraping Multiple URLs

```python
async def scrape_multiple():
    urls = [
        "https://metruyencv.com/truyen/example/chuong-1",
        "https://metruyencv.com/truyen/example/chuong-2",
        "https://metruyencv.com/truyen/example/chuong-3"
    ]
    
    async with MetruyenScraper() as scraper:
        results = await scraper.scrape_urls(urls, max_concurrent=2)
        print(f"Scraped {len(results)} URLs successfully")
```

### Scraping Story Chapters

```python
async def scrape_story():
    story_url = "https://metruyencv.com/truyen/example-story"
    
    async with MetruyenScraper() as scraper:
        # Scrape all chapters (or limit with max_chapters)
        results = await scraper.scrape_story_chapters(story_url, max_chapters=10)
        print(f"Scraped {len(results)} chapters")
```

## Configuration

The scraper uses a YAML configuration file (`config.yaml`) for customization:

### Browser Settings
```yaml
scraper:
  browser:
    headless: true
    timeout: 30000
    viewport:
      width: 1920
      height: 1080
```

### Anti-Detection Settings
```yaml
scraper:
  stealth:
    user_agents:
      - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
    delays:
      min_delay: 2
      max_delay: 5
    proxy:
      enabled: false
      rotation: false
```

### Target Website Configuration
```yaml
targets:
  metruyencv:
    base_url: "https://metruyencv.com"
    selectors:
      title: "h1, .title"
      content: ".content, .chapter-content"
      next_chapter: "a:contains('Chương sau')"
```

## Advanced Usage

### Custom Selectors

```python
# Modify configuration for different websites
config = ConfigManager()
config.update_config('targets.custom_site', {
    'selectors': {
        'title': 'h1.custom-title',
        'content': '.custom-content'
    }
})
```

### Error Handling

```python
async with MetruyenScraper() as scraper:
    # Get error statistics
    stats = scraper.get_statistics()
    print(f"Error rate: {stats['error_statistics']}")
    
    # Get failed URLs
    failed_urls = stats['failed_urls']
    for failed in failed_urls:
        print(f"Failed: {failed['url']} - {failed['error_type']}")
```

### Proxy Configuration

```yaml
scraper:
  stealth:
    proxy:
      enabled: true
      rotation: true
      list:
        - server: "http://proxy1:port"
          username: "user"
          password: "pass"
```

## Project Structure

```
MetruyenScraper/
├── src/
│   ├── __init__.py
│   ├── metruyenscraper.py      # Main scraper class
│   ├── scraper_engine.py       # Playwright browser engine
│   ├── anti_detection.py       # Anti-bot detection bypass
│   ├── config_manager.py       # Configuration management
│   ├── error_handler.py        # Error handling and retry logic
│   └── data_processor.py       # Data processing and export
├── config.yaml                 # Configuration file
├── requirements.txt            # Python dependencies
├── example_usage.py           # Usage examples
└── README.md                  # This file
```

## Examples

Run the provided examples to see the scraper in action:

```bash
python example_usage.py
```

The examples demonstrate:
- Single URL scraping
- Multiple URL scraping
- Story chapter scraping
- Error handling
- Data export
- Custom configuration

## Handling Protected Content

The scraper is designed to handle various protection mechanisms:

### Locked Content
- Automatically detects locked/premium content
- Marks content as locked in the data
- Continues scraping available content

### CAPTCHA Detection
- Detects CAPTCHA challenges
- Takes screenshots for manual review
- Implements waiting strategies

### Rate Limiting
- Detects rate limiting responses
- Implements exponential backoff
- Respects server-specified retry delays

## Data Export Formats

### JSON Export
```json
{
  "url": "https://example.com/page",
  "title": "Page Title",
  "content": "Page content...",
  "is_locked": false,
  "timestamp": 1640995200.0,
  "navigation": {
    "next_chapter": "https://example.com/next",
    "chapters": [...]
  }
}
```

### CSV Export
Flattened data structure suitable for spreadsheet analysis.

### Statistics
```json
{
  "data_statistics": {
    "total_items": 10,
    "locked_content": 2,
    "with_content": 8
  },
  "error_statistics": {
    "total_errors": 1,
    "error_breakdown": {
      "network_error": 1
    }
  }
}
```

## Best Practices

1. **Respect Rate Limits**: Configure appropriate delays between requests
2. **Monitor Error Rates**: Check statistics regularly and adjust configuration
3. **Use Headless Mode**: For production, use headless browser mode
4. **Rotate User Agents**: Keep user agent list updated
5. **Handle Failures Gracefully**: Implement proper error handling in your code

## Troubleshooting

### Common Issues

1. **Playwright Installation**: Make sure to install browser binaries
2. **Timeout Errors**: Increase timeout values in configuration
3. **CAPTCHA Challenges**: May require manual intervention
4. **Rate Limiting**: Increase delays between requests

### Debug Mode

Enable debug logging by modifying the configuration:

```yaml
logging:
  level: "DEBUG"
  file: "scraper.log"
```

## Legal Considerations

- Always respect robots.txt and website terms of service
- Implement appropriate delays to avoid overloading servers
- Consider reaching out to website owners for permission
- Use scraped data responsibly and in compliance with applicable laws

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is provided for educational purposes. Please ensure compliance with applicable laws and website terms of service when using this scraper.
